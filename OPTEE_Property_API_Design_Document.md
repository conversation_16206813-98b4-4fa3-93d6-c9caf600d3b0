# OPTEE属性获取相关GP API设计文档

## 1. 概述

本文档详细分析OPTEE中属性获取相关的GlobalPlatform (GP) API设计，包括API函数实现、数据结构定义、属性类型、存储位置以及系统调用机制。

## 2. GP API函数列表

### 2.1 属性获取函数
- `TEE_GetPropertyAsString` - 获取字符串类型属性
- `TEE_GetPropertyAsBool` - 获取布尔类型属性  
- `TEE_GetPropertyAsU32` - 获取32位无符号整数属性
- `TEE_GetPropertyAsU64` - 获取64位无符号整数属性
- `TEE_GetPropertyAsBinaryBlock` - 获取二进制块属性
- `TEE_GetPropertyAsUUID` - 获取UUID类型属性
- `TEE_GetPropertyAsIdentity` - 获取身份标识属性

### 2.2 属性枚举函数
- `TEE_AllocatePropertyEnumerator` - 分配属性枚举器
- `TEE_FreePropertyEnumerator` - 释放属性枚举器
- `TEE_StartPropertyEnumerator` - 启动属性枚举
- `TEE_ResetPropertyEnumerator` - 重置属性枚举器
- `TEE_GetPropertyName` - 获取属性名称
- `TEE_GetNextProperty` - 获取下一个属性

## 3. 核心数据结构

### 3.1 属性类型枚举

```c
// lib/libutee/include/user_ta_header.h
enum user_ta_prop_type {
    USER_TA_PROP_TYPE_BOOL,         // bool类型
    USER_TA_PROP_TYPE_U32,          // uint32_t类型
    USER_TA_PROP_TYPE_UUID,         // TEE_UUID类型
    USER_TA_PROP_TYPE_IDENTITY,     // TEE_Identity类型
    USER_TA_PROP_TYPE_STRING,       // 零终止字符串
    USER_TA_PROP_TYPE_BINARY_BLOCK, // base64编码的二进制数据
    USER_TA_PROP_TYPE_U64,          // uint64_t类型
    USER_TA_PROP_TYPE_INVALID,      // 无效类型
};
```

### 3.2 用户TA属性结构

```c
// lib/libutee/include/user_ta_header.h
struct user_ta_property {
    const char *name;                    // 属性名称
    enum user_ta_prop_type type;         // 属性类型
    const void *value;                   // 属性值指针
};
```

### 3.3 TEE属性结构

```c
// core/include/tee/tee_svc.h
struct tee_props {
    const char *name;                    // 属性名称
    const uint32_t prop_type;           // 属性类型(enum user_ta_prop_type)
    
    // 获取属性的函数指针或静态数据
    TEE_Result (*get_prop_func)(struct ts_session *sess,
                               void *buf, size_t *blen);
    const void *data;                   // 静态属性数据
    const size_t len;                   // 数据长度
};
```

### 3.4 属性枚举器结构

```c
// lib/libutee/tee_api_property.c
struct prop_enumerator {
    uint32_t idx;                       // 当前索引
    TEE_PropSetHandle prop_set;         // 属性集句柄(TEE_PROPSET_xxx)
};
```

## 4. 基础数据类型

### 4.1 TEE_UUID结构

```c
// lib/libutee/include/tee_api_types.h
typedef struct {
    uint32_t timeLow;                   // 时间低32位
    uint16_t timeMid;                   // 时间中16位
    uint16_t timeHiAndVersion;          // 时间高16位和版本
    uint8_t clockSeqAndNode[8];         // 时钟序列和节点标识
} TEE_UUID;
```

### 4.2 TEE_Identity结构

```c
// lib/libutee/include/tee_api_types.h
typedef struct {
    uint32_t login;                     // 登录类型(TEE_LOGIN_xxx)
    TEE_UUID uuid;                      // 客户端UUID
} TEE_Identity;
```

### 4.3 属性集句柄类型

```c
// lib/libutee/include/tee_api_types.h
typedef struct __TEE_PropSetHandle *TEE_PropSetHandle;
```

### 4.4 属性集伪句柄定义

```c
// lib/libutee/include/tee_api_defines.h
#define TEE_PROPSET_TEE_IMPLEMENTATION  (TEE_PropSetHandle)0xFFFFFFFD
#define TEE_PROPSET_CURRENT_CLIENT      (TEE_PropSetHandle)0xFFFFFFFE  
#define TEE_PROPSET_CURRENT_TA          (TEE_PropSetHandle)0xFFFFFFFF
```

## 5. 属性存储位置和分类

### 5.1 TEE实现属性 (TEE_PROPSET_TEE_IMPLEMENTATION)

**位置**: `core/tee/tee_svc.c` - `tee_propset_tee[]`

**主要属性**:
- `gpd.tee.apiversion` - TEE API版本 (STRING)
- `gpd.tee.description` - TEE描述信息 (STRING)  
- `gpd.tee.deviceID` - 设备ID (UUID)
- `gpd.tee.systemTime.protectionLevel` - 系统时间保护级别 (U32)
- `gpd.tee.TAPersistentTime.protectionLevel` - TA持久时间保护级别 (U32)

### 5.2 当前客户端属性 (TEE_PROPSET_CURRENT_CLIENT)

**位置**: `core/tee/tee_svc.c` - `tee_propset_client[]`

**主要属性**:
- `gpd.client.identity` - 客户端身份标识 (IDENTITY)
- `gpd.client.endian` - 客户端字节序 (U32)

### 5.3 当前TA属性 (TEE_PROPSET_CURRENT_TA)

**位置**: `lib/libutee/tee_api_property.c` - `tee_props[]`

**主要属性**:
- `gpd.tee.arith.maxBigIntSize` - 大整数最大位数 (U32)
- `gpd.tee.sockets.version` - Socket版本 (U32)
- `gpd.tee.sockets.tcp.version` - TCP Socket版本 (U32)
- `gpd.tee.internalCore.version` - 内核API版本 (U32)

**扩展属性**: TA可通过`TA_CURRENT_TA_EXT_PROPERTIES`宏定义自定义属性

## 6. API函数详细实现分析

### 6.1 TEE_GetPropertyAsString

**位置**: `lib/libutee/tee_api_property.c:198`

**函数签名**:
```c
TEE_Result TEE_GetPropertyAsString(TEE_PropSetHandle propsetOrEnumerator,
                                   const char *name, char *value,
                                   size_t *value_len);
```

**实现特点**:
- 支持所有属性类型到字符串的转换
- 对于BOOL类型：转换为"true"/"false"
- 对于U32类型：使用`snprintf`格式化为十进制字符串
- 对于UUID类型：使用`snprintk`格式化为标准UUID字符串格式
- 对于IDENTITY类型：格式化为"login:uuid"格式
- 对于BINARY_BLOCK类型：使用base64编码
- 自动处理缓冲区大小和内存分配

### 6.2 TEE_GetPropertyAsBool

**位置**: `lib/libutee/tee_api_property.c:314`

**函数签名**:
```c
TEE_Result TEE_GetPropertyAsBool(TEE_PropSetHandle propsetOrEnumerator,
                                 const char *name, bool *value);
```

**实现特点**:
- 严格类型检查：只接受USER_TA_PROP_TYPE_BOOL类型
- 直接内存拷贝，无需类型转换
- 错误时返回TEE_ERROR_BAD_FORMAT

### 6.3 TEE_GetPropertyAsU32/U64

**位置**: `lib/libutee/tee_api_property.c:342/367`

**函数签名**:
```c
TEE_Result TEE_GetPropertyAsU32(TEE_PropSetHandle propsetOrEnumerator,
                                const char *name, uint32_t *value);
TEE_Result TEE_GetPropertyAsU64(TEE_PropSetHandle propsetOrEnumerator,
                                const char *name, uint64_t *value);
```

**实现特点**:
- 严格类型检查：只接受对应的U32/U64类型
- 直接内存拷贝获取数值
- 类型不匹配时返回TEE_ERROR_BAD_FORMAT

### 6.4 TEE_GetPropertyAsBinaryBlock

**位置**: `lib/libutee/tee_api_property.c:419`

**函数签名**:
```c
TEE_Result TEE_GetPropertyAsBinaryBlock(TEE_PropSetHandle propsetOrEnumerator,
                                        const char *name, void *value,
                                        size_t *value_len);
```

**实现特点**:
- 支持GP 1.1版本兼容性（__GP11_TEE_GetPropertyAsBinaryBlock）
- 对于TA属性：自动进行base64解码
- 缓冲区大小检查和错误处理

### 6.5 TEE_GetPropertyAsUUID

**位置**: `lib/libutee/tee_api_property.c:434`

**函数签名**:
```c
TEE_Result TEE_GetPropertyAsUUID(TEE_PropSetHandle propsetOrEnumerator,
                                 const char *name, TEE_UUID *value);
```

**实现特点**:
- 严格类型检查：只接受USER_TA_PROP_TYPE_UUID类型
- 直接拷贝16字节UUID结构
- 支持设备ID等系统UUID属性

### 6.6 TEE_GetPropertyAsIdentity

**位置**: `lib/libutee/tee_api_property.c:459`

**函数签名**:
```c
TEE_Result TEE_GetPropertyAsIdentity(TEE_PropSetHandle propsetOrEnumerator,
                                     const char *name, TEE_Identity *value);
```

**实现特点**:
- 严格类型检查：只接受USER_TA_PROP_TYPE_IDENTITY类型
- 拷贝完整的TEE_Identity结构（login + uuid）
- 主要用于获取客户端身份信息

## 7. 属性枚举器实现

### 7.1 TEE_AllocatePropertyEnumerator

**位置**: `lib/libutee/tee_api_property.c:484`

**函数签名**:
```c
TEE_Result TEE_AllocatePropertyEnumerator(TEE_PropSetHandle *enumerator);
```

**实现特点**:
- 动态分配prop_enumerator结构
- 自动调用TEE_ResetPropertyEnumerator初始化
- 内存不足时返回TEE_ERROR_OUT_OF_MEMORY

### 7.2 TEE_StartPropertyEnumerator

**位置**: `lib/libutee/tee_api_property.c:525`

**函数签名**:
```c
void TEE_StartPropertyEnumerator(TEE_PropSetHandle enumerator,
                                 TEE_PropSetHandle propSet);
```

**实现特点**:
- 设置枚举器的属性集和起始索引
- 索引从0开始
- 支持三种属性集的枚举

### 7.3 TEE_GetNextProperty

**位置**: `lib/libutee/tee_api_property.c:593`

**函数签名**:
```c
TEE_Result TEE_GetNextProperty(TEE_PropSetHandle enumerator);
```

**实现特点**:
- 递增枚举器索引
- 先枚举libutee中的静态属性
- 然后通过系统调用枚举内核属性
- 枚举结束时返回TEE_ERROR_ITEM_NOT_FOUND

## 8. 系统调用机制

### 8.1 系统调用定义

**位置**: `lib/libutee/include/utee_syscalls.h`

```c
// 获取属性值
TEE_Result _utee_get_property(unsigned long prop_set, unsigned long index,
                              void *name, uint32_t *name_len, void *buf,
                              uint32_t *blen, uint32_t *prop_type);

// 根据属性名获取索引
TEE_Result _utee_get_property_name_to_index(unsigned long prop_set,
                                            const void *name,
                                            unsigned long name_len,
                                            uint32_t *index);
```

### 8.2 系统调用汇编入口

**位置**: `lib/libutee/include/utee_syscalls_asm.S`

```assembly
UTEE_SYSCALL _utee_get_property, TEE_SCN_GET_PROPERTY, 7
UTEE_SYSCALL _utee_get_property_name_to_index, TEE_SCN_GET_PROPERTY_NAME_TO_INDEX, 4
```

### 8.3 内核系统调用实现

**位置**: `core/tee/tee_svc.c`

```c
// 系统调用处理函数
TEE_Result syscall_get_property(unsigned long prop_set,
                                unsigned long index,
                                void *name, uint32_t *name_len,
                                void *buf, uint32_t *blen,
                                uint32_t *prop_type);

TEE_Result syscall_get_property_name_to_index(unsigned long prop_set,
                                              void *name,
                                              unsigned long name_len,
                                              uint32_t *index);
```

### 8.4 系统调用表注册

**位置**: `core/kernel/scall.c`

```c
static const struct syscall_entry tee_syscall_table[] = {
    // ...
    SYSCALL_ENTRY(syscall_get_property),
    SYSCALL_ENTRY(syscall_get_property_name_to_index),
    // ...
};
```

## 9. 属性获取流程分析

### 9.1 核心获取函数：propget_get_property

**位置**: `lib/libutee/tee_api_property.c:142`

**流程**:
1. **句柄类型判断**: 使用`is_propset_pseudo_handle()`判断是否为伪句柄
2. **伪句柄处理**:
   - 调用`propset_get()`获取libutee中的静态属性数组
   - 遍历数组查找匹配的属性名
   - 找到则调用`propget_get_ext_prop()`处理
   - 未找到则通过系统调用查询内核属性
3. **枚举器处理**:
   - 从枚举器中获取当前索引和属性集
   - 根据索引直接访问对应属性

### 9.2 属性集获取函数：propset_get

**位置**: `lib/libutee/tee_api_property.c:56`

**功能**: 根据属性集句柄返回对应的属性数组
- `TEE_PROPSET_CURRENT_TA`: 返回`ta_props`数组
- `TEE_PROPSET_CURRENT_CLIENT`: 返回空数组（由内核处理）
- `TEE_PROPSET_TEE_IMPLEMENTATION`: 返回`tee_props`数组

### 9.3 扩展属性处理：propget_get_ext_prop

**位置**: `lib/libutee/tee_api_property.c:76`

**功能**: 处理libutee中定义的静态属性
- 根据属性类型确定数据长度
- 对于BINARY_BLOCK类型进行base64解码
- 执行缓冲区大小检查和数据拷贝

### 9.4 内核属性获取流程

**内核端处理** (`core/tee/tee_svc.c:462`):
1. **属性集验证**: 调用`get_prop_set()`获取属性数组
2. **索引验证**: 检查索引是否在有效范围内
3. **属性获取**:
   - 静态数据：直接从`data`字段拷贝
   - 动态数据：调用`get_prop_func`函数获取
4. **数据拷贝**: 使用`copy_to_user()`安全拷贝到用户空间

## 10. 错误处理机制

### 10.1 常见错误码

- `TEE_SUCCESS`: 操作成功
- `TEE_ERROR_ITEM_NOT_FOUND`: 属性不存在
- `TEE_ERROR_SHORT_BUFFER`: 缓冲区太小
- `TEE_ERROR_BAD_FORMAT`: 属性类型不匹配
- `TEE_ERROR_BAD_PARAMETERS`: 参数无效
- `TEE_ERROR_OUT_OF_MEMORY`: 内存不足

### 10.2 错误处理策略

**API层错误处理**:
- 类型不匹配时返回`TEE_ERROR_BAD_FORMAT`
- 严重错误时调用`TEE_Panic(0)`终止TA
- 缓冲区不足时返回所需大小

**系统调用层错误处理**:
- 参数验证失败返回`TEE_ERROR_BAD_PARAMETERS`
- 内存拷贝失败返回相应错误码
- 属性不存在返回`TEE_ERROR_ITEM_NOT_FOUND`

## 11. 使用示例

### 11.1 获取字符串属性

```c
TEE_Result get_api_version(void) {
    char version[64];
    size_t version_len = sizeof(version);
    TEE_Result res;

    res = TEE_GetPropertyAsString(TEE_PROPSET_TEE_IMPLEMENTATION,
                                  "gpd.tee.apiversion",
                                  version, &version_len);
    if (res == TEE_SUCCESS) {
        DMSG("TEE API Version: %s", version);
    }
    return res;
}
```

### 11.2 获取客户端身份

```c
TEE_Result get_client_identity(void) {
    TEE_Identity client_id;
    TEE_Result res;

    res = TEE_GetPropertyAsIdentity(TEE_PROPSET_CURRENT_CLIENT,
                                    "gpd.client.identity",
                                    &client_id);
    if (res == TEE_SUCCESS) {
        DMSG("Client login: %u", client_id.login);
        DMSG("Client UUID: %pUl", &client_id.uuid);
    }
    return res;
}
```

### 11.3 枚举所有属性

```c
TEE_Result enumerate_properties(TEE_PropSetHandle prop_set) {
    TEE_PropSetHandle enumerator = TEE_HANDLE_NULL;
    TEE_Result res;
    char name[128];
    size_t name_len;

    // 分配枚举器
    res = TEE_AllocatePropertyEnumerator(&enumerator);
    if (res != TEE_SUCCESS)
        return res;

    // 启动枚举
    TEE_StartPropertyEnumerator(enumerator, prop_set);

    // 枚举所有属性
    while (TEE_GetNextProperty(enumerator) == TEE_SUCCESS) {
        name_len = sizeof(name);
        res = TEE_GetPropertyName(enumerator, name, &name_len);
        if (res == TEE_SUCCESS) {
            DMSG("Property: %s", name);
        }
    }

    // 释放枚举器
    TEE_FreePropertyEnumerator(enumerator);
    return TEE_SUCCESS;
}
```

### 11.4 获取数值属性

```c
TEE_Result get_bigint_max_size(void) {
    uint32_t max_bits;
    TEE_Result res;

    res = TEE_GetPropertyAsU32(TEE_PROPSET_CURRENT_TA,
                               "gpd.tee.arith.maxBigIntSize",
                               &max_bits);
    if (res == TEE_SUCCESS) {
        DMSG("Max BigInt size: %u bits", max_bits);
    }
    return res;
}
```

## 12. 架构设计总结

### 12.1 分层架构

```
┌─────────────────────────────────────────────────────────────┐
│                    GP API Layer                             │
│  TEE_GetPropertyAsXXX / TEE_XXXPropertyEnumerator          │
├─────────────────────────────────────────────────────────────┤
│                  Property Management                        │
│  propget_get_property / propset_get / prop_enumerator      │
├─────────────────────────────────────────────────────────────┤
│                   System Call Layer                        │
│  _utee_get_property / _utee_get_property_name_to_index     │
├─────────────────────────────────────────────────────────────┤
│                    Kernel Layer                            │
│  syscall_get_property / tee_propset_xxx arrays            │
└─────────────────────────────────────────────────────────────┘
```

### 12.2 数据流向

```
TA Application
     ↓ (API调用)
GP API Functions (libutee)
     ↓ (属性查找)
Property Management (propget_get_property)
     ↓ (系统调用)
Kernel Property Handler (syscall_get_property)
     ↓ (数据获取)
Property Storage (tee_propset_xxx / get_prop_func)
```

### 12.3 属性存储分布

```
┌─────────────────┬─────────────────┬─────────────────┐
│   CURRENT_TA    │ CURRENT_CLIENT  │ TEE_IMPL        │
├─────────────────┼─────────────────┼─────────────────┤
│ libutee静态属性  │   内核动态属性   │  内核静态/动态   │
│ tee_props[]     │ get_prop_func   │ tee_propset_tee │
│ TA自定义属性     │ 客户端身份信息   │ TEE实现信息      │
└─────────────────┴─────────────────┴─────────────────┘
```

### 12.4 关键设计特点

1. **类型安全**: 严格的类型检查确保数据完整性
2. **内存安全**: 缓冲区大小检查和安全拷贝机制
3. **扩展性**: 支持TA自定义属性和厂商扩展属性
4. **性能优化**: 静态属性缓存和动态属性按需获取
5. **错误处理**: 完善的错误码体系和异常处理机制

## 13. 文件位置总结

### 13.1 头文件
- `lib/libutee/include/tee_internal_api.h` - GP API函数声明
- `lib/libutee/include/tee_api_types.h` - 基础数据类型定义
- `lib/libutee/include/tee_api_defines.h` - 常量定义
- `lib/libutee/include/user_ta_header.h` - 属性类型和结构定义
- `lib/libutee/include/utee_syscalls.h` - 系统调用声明
- `core/include/tee/tee_svc.h` - 内核属性结构定义

### 13.2 实现文件
- `lib/libutee/tee_api_property.c` - GP API主要实现
- `core/tee/tee_svc.c` - 内核属性处理和系统调用实现
- `core/kernel/scall.c` - 系统调用表注册
- `lib/libutee/arch/arm/utee_syscalls_asm.S` - 系统调用汇编入口

### 13.3 示例文件
- `ta/gatekeeper/src/include/user_ta_header_defines.h` - TA自定义属性示例
